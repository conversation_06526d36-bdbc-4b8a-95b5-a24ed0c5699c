import { Model, DataTypes, WhereOptions, Op, Sequelize, DestroyOptions } from 'sequelize';

import { Literal } from 'sequelize/types/utils';
import { includes, startCase, uniq } from 'lodash';
import { AwardInput, AwardOuput, AwardStatus } from './types/award';
import { UserOuput } from './types/user';
import { CustomFindOptions } from './types/shared';
import { sequelize as connection } from '../config/database';
import strings from '../config/strings';
import { parseHTMLTemplate, sendEmailLetter } from '../helpers/mail.helper';
import { AwardNotificationInput } from './types/award-notification';
import { AwardUserRoleOuput } from './types/award-user-role';
import modelHelper from '../helpers/model-helper';

const AwardBudgetEntry = require('./award-budget-entries').default(connection);
const AwardFile = require('./award-files').default(connection);
const Client = require('./clients').default(connection);
const User = require('./employees').default(connection);
const AwardNotification = require('./award-notifications').default(connection);
const AwardUserRole = require('./award-user-roles').default(connection);

function generateCustomId() {
  return modelHelper.generateUniqueId('DEV-');
}

const approvalUserTypes = [strings.users.userTypes.userAdmin];

export default function (sequelize: Sequelize) {
  class Awards extends Model<AwardInput> {
    public id!: number;

    public stringId!: number;

    public programId!: number;

    public assigneeId!: number;

    public clientId!: string;

    public applicationId!: string;

    public reportId!: string;

    public status!: AwardStatus;

    public source!: number;

    public category!: number;

    public funder!: string;

    public startsOn!: Date;

    public endsOn!: Date | string | null;

    public department!: string;

    public grantPurpose!: string;

    public grantProgramName!: string;

    public notes!: string;

    public dateApproved!: Date;

    public dateLastRejected!: Date;

    public isApproved!: boolean;

    public awardAmount!: number;

    public awardExpended!: number;

    public awardBalance!: number;

    public matchAmount!: number;

    public matchExpended!: number;

    public matchBalance!: number;

    public paymentsRequested!: number;

    public paymentsReceived!: number;

    public resolutionRequired!: boolean;

    public applicationRequired!: boolean;

    public awardLetterRequired!: boolean;

    public contractMaterialRequired!: boolean;

    public initialAgreementRequired!: boolean;

    public reportRequired!: boolean;

    public funderAwardId!: string;

    public awardPortalLink!: string;

    public enabled!: boolean;

    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models: any) {
      Awards.belongsTo(models.Client, {
        foreignKey: 'client_id',
        as: 'client',
      });

      Awards.belongsTo(models.Application, {
        foreignKey: 'application_id',
        as: 'application',
      });

      Awards.belongsTo(models.Program, {
        foreignKey: 'program_id',
        as: 'program',
      });

      Awards.belongsTo(models.Employee, {
        foreignKey: 'assignee_id',
        as: 'assignee',
      });

      Awards.belongsToMany(models.Project, {
        through: models.ProjectAward,
        as: 'projects',
        foreignKey: 'awardId',
      });

      Awards.hasMany(models.AwardBudgetEntry, {
        foreignKey: 'award_id',
        as: 'budgetEntries',
      });

      Awards.hasMany(models.AwardFile, {
        foreignKey: 'award_id',
        as: 'files',
      });

      Awards.hasMany(models.AwardPayment, {
        foreignKey: 'award_id',
        as: 'payments',
      });

      Awards.hasMany(models.AwardReport, {
        foreignKey: 'award_id',
        as: 'reports',
      });

      Awards.hasMany(models.AwardUserRole, {
        foreignKey: 'award_id',
        as: 'userRoles',
      });

      Awards.hasMany(models.AwardNotification, {
        foreignKey: 'source_id',
        constraints: false,
        scope: {
          sourceType: 'award',
        },
        as: 'notifications',
      });

      Awards.hasMany(models.ActionLog, {
        foreignKey: 'award_id',
        as: 'actionLogs',
      });
    }

    static async findByQuery(field: string, query: string, attributes: (string | Literal)[]) {
      const whereClause: WhereOptions = {};
      const queriedField = Awards.rawAttributes[field];

      if (!queriedField) {
        throw new Error(`The column '${field}' doesn't exist in table 'awards'.`);
      } else if (queriedField.type instanceof DataTypes.STRING) {
        whereClause[field] = { [Op.iLike]: `%${query}%` };
      } else if (queriedField.type instanceof DataTypes.INTEGER) {
        const intQuery = parseInt(query, 10);
        if (Number.isNaN(intQuery)) {
          throw new Error(`Incompatible query type for column '${field}'.`);
        } else {
          whereClause[field] = { [Op.eq]: intQuery };
        }
      }

      console.log('Award -> Search -> whereClause', whereClause);

      return Awards.findAll({
        attributes,
        where: whereClause,
        nodetails: true,
      } as CustomFindOptions);
    }

    static async createFile(options: any) {
      const { file } = options;
      const { data, name, mimetype } = file;

      const { length } = data;

      return AwardFile.create({
        ...options,
        name,
        url: data,
        size: length,
        type: mimetype,
      });
    }

    static async destroyFile(options: DestroyOptions) {
      return AwardFile.destroy(options);
    }

    static getPreviousStatus(award: AwardInput) {
      const statuses = Object.keys(strings.awards.statusStrings);
      const previousStatus =
        statuses[
          Object.keys(strings.awards.statusStrings).indexOf(award.status || 'applicationRequired') -
            1
        ];
      return previousStatus;
    }

    static async sendStatusChangeEmail(award: AwardInput, isUpdate = false) {
      try {
        const client = await Client.findByPk(award.clientId);

        if (!client || !client.dataValues || !client.dataValues.awardsEnabled) return;

        // Check for private award management
        const { privateAwardsManagement = false } = client.dataValues;

        const awardRoles: AwardUserRoleOuput[] = await AwardUserRole.findAll({
          where: { awardId: award.id, enabled: true },
        });

        const { usersCreatedByClient = [], awardUsers = [] } = client?.dataValues || {};

        // Filter users based on privateAwardsManagement setting
        let userIds = [];
        if (privateAwardsManagement) {
          userIds = uniq(
            [
              ...(usersCreatedByClient || [])
                .filter((u: typeof User) => u?.userType === strings.users.userTypes.millenniumAdmin)
                .map((user: typeof User) => user?.id),
            ].filter(Boolean)
          );
        } else {
          userIds = uniq(
            [
              ...[
                ...((usersCreatedByClient || [])?.filter(
                  (u: typeof User) => u && u.userType.match(/admin/i)
                ) || []),
                ...(awardUsers || []),
              ].map((user) => user && user.id),
              ...(awardRoles || []).map((role) => role.userId),
              award.assigneeId,
            ].filter(Boolean)
          );
        }

        let users: UserOuput[] = await User.findAll({ where: { id: userIds } });

        const emailAttributes: any = {};

        const awardLink = `${process.env.PUBLIC_URL}dashboard/award/${award.id}/details`;

        // TODO: Refactor this so that there are a static list of status array items.
        const previousStatus = Awards.getPreviousStatus(award);

        let subject = '';
        let description: string | null = null;

        // console.log(
        //   '***',
        //   'sendStatusChangeEmail',
        //   userIds,
        //   usersCreatedByClient?.map((u: typeof User) => u.userType),
        //   isUpdate,
        //   award.status,
        //   previousStatus
        // );

        if (isUpdate && award.status === 'applicationRequired') return;
        if (award.status === 'encumbered' && award.dateLastRejected) return;

        switch (award.status) {
          case 'applicationRequired':
            subject = `A new award is created, please assign roles`;
            description = 'Assign a Project Director to get the award management process started.';

            // Only filter for admin if not privately managed
            if (!privateAwardsManagement) {
              users = users.filter((user) => user.userType.toString().match(/admin/i));
            }

            emailAttributes.hideStatusChange = true;
            break;
          case 'approval':
            subject = `Award #${award.stringId || ''} has been submitted for approval`;

            if (!privateAwardsManagement) {
              users = uniq(
                [
                  ...usersCreatedByClient.map((user: typeof User) =>
                    approvalUserTypes.includes(user.userType)
                  ),
                ].filter((user) => user)
              );
            }
            break;
          case 'active':
            subject = `Award #${award.stringId || ''} is now active`;
            break;
          case 'encumbered':
            subject = `Award #${award.stringId || ''} is now encumbered`;
            emailAttributes.canApprove = true;
            break;
          case 'budgetRequired':
            subject = `Award #${award.stringId || ''} is now appropriated`;
            break;
          case 'closeout':
            subject = `Award #${award.stringId || ''} is now in closout`;
            break;
          case 'closed':
            subject = `Award #${award.stringId || ''} is now closed`;
            break;
          default:
            subject = `Award #${award.stringId || ''} changed from "${startCase(
              previousStatus || 'Awarded'
            )}" to "${startCase(award.status)}"`;

            if (!privateAwardsManagement) {
              users = [users.filter((user) => user.id === award.assigneeId)[0]];
            }
            break;
        }

        // Uncomment to debug
        // console.log(
        //   '***',
        //   'sendStatusChangeEmail',
        //   subject,
        //   userIds.join(', '),
        //   users.length,
        //   users.map((user) => user.userRole?.role).join(', '),
        //   users.map((user) => user.userRoles?.map((ur) => ur?.role).join(', ')).join(', '),
        //   users.map((user) => user.email).join(', ')
        // );

        // Only send emails if we have users to send to
        if (users.length > 0) {
          Promise.all(
            users.map((user) => {
              if (!user?.email) return null;

              const userRole =
                user.userRole || awardRoles.filter((role) => role.userId === user.id)[0];
              const isProjectDirector = userRole?.role === 'projectDirector';

              const formattedAwardAmount = new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
              }).format(Number(award.awardAmount));

              return sendEmailLetter({
                from: process.env.SERVICE_MAILER_EMAIL,
                to: user.email,
                subject,
                plainver: `View award details here: ${awardLink}`,
                htmlver: parseHTMLTemplate(
                  `${__dirname}/../email_templates/award-status-change.html`,
                  {
                    ...emailAttributes,
                    username: user.name,
                    awardLink,
                    ...award,
                    awardAmount: formattedAwardAmount,
                    status: `${startCase(award.status)}`,
                    previousStatus: `${startCase(previousStatus)}`,
                    description,
                    isAppropriated: award.status === 'contractMaterialRequired',
                    isApproval: award.status === 'approval',
                    isProjectDirector,
                    canApprove: emailAttributes.canApprove && isProjectDirector,
                  }
                ),
              });
            })
          );
        }
      } catch (error) {
        console.error('sendStatusChangeEmail', error);
        throw new Error('An error has occurred while sending a status change email.');
      }
    }

    static async createStatusChangeNotifications(award: AwardInput, previous: any) {
      try {
        const client = await Client.findByPk(award.clientId);

        // Early return if client not found
        if (!client) return;

        const awardRoles: AwardUserRoleOuput[] = await AwardUserRole.findAll({
          where: { awardId: award.id, enabled: true },
        });

        const {
          usersCreatedByClient = [],
          awardUsers = [],
          privateAwardsManagement = false,
        } = client?.dataValues || {};

        // If private award management is enabled, only include Millennium admin users
        let userIds = [];
        if (privateAwardsManagement) {
          // Only include Millennium admin users
          userIds = uniq(
            [
              ...(usersCreatedByClient || [])
                .filter(
                  (user: typeof User) => user?.userType === strings.users.userTypes.millenniumAdmin
                )
                .map((user: typeof User) => user?.id),
            ].filter(Boolean)
          );
        } else {
          // Include all relevant users if private management is disabled
          userIds = uniq(
            [
              ...(awardRoles || []).map((awardRole) => awardRole.userId),
              ...[...(usersCreatedByClient || []), ...(awardUsers || [])].map((user) => user?.id),
              award.assigneeId,
            ].filter(Boolean)
          );
        }

        const { status } = award;
        const data: AwardNotificationInput = {
          sourceId: award.id || 0,
          sourceType: 'award',
        };
        const previousStatus = Awards.getPreviousStatus(award);

        switch (status) {
          case 'approval':
            data.type = 'approvalRequest';
            data.subject = `Award ${award.stringId || ''} has been submitted for approval`;

            if (!privateAwardsManagement) {
              userIds = uniq(
                [
                  ...awardRoles.map(
                    (awardRole: typeof AwardUserRole) =>
                      awardRole.role === 'financeContact' && awardRole.userId
                  ),
                  ...usersCreatedByClient.map((user: typeof User) =>
                    approvalUserTypes.includes(user.userType)
                  ),
                ].filter(Boolean)
              );
            }
            break;
          case 'active':
            data.type = 'approved';
            data.subject = `Award ${award.stringId || ''} was approved`;
            break;
          case 'applicationRequired':
          case 'awardLetterRequired':
          case 'initialAgreementRequired':
          case 'contractMaterialRequired':
          case 'resolutionRequired':
          case 'budgetRequired':
            data.type = 'documentReminder';
            data.subject = `${startCase(status)} for Award #${award.stringId ?? ''}`;
            break;
          default:
            data.subject = `Status for Award #${award.stringId ?? ''} changed from "${startCase(
              previousStatus
            )}" to "${startCase(status)}"`;
            break;
        }

        if (data.subject && userIds.length > 0) {
          AwardNotification.bulkCreate(
            userIds.map((userId) => ({
              ...data,
              userId,
            }))
          );
        }
      } catch (error) {
        console.error('createStatusChangeNotifications', error);
        throw new Error('An error has occurred while sending a status change email.');
      }
    }

    static async updateAssigneeRole(award: AwardInput) {
      const newAssigneeId = award.assigneeId;

      // disable the user role instead of destroying
      await AwardUserRole.update(
        {
          enabled: false,
        },
        {
          where: {
            awardId: award.id,
            [Op.or]: [
              {
                role: 'projectDirector',
              },
              {
                userId: newAssigneeId,
              },
            ],
          },
        }
      );

      await AwardUserRole.create({
        role: 'projectDirector',
        userId: newAssigneeId,
        awardId: award.id,
      });

      return AwardUserRole.sendRoleChangeEmail(award.id, 'projectDirector', newAssigneeId);
    }
  }

  Awards.init(
    {
      stringId: {
        field: 'string_id',
        type: DataTypes.STRING,
        defaultValue: null,
      },
      programId: {
        field: 'program_id',
        type: DataTypes.INTEGER,
        defaultValue: null,
      },
      applicationId: {
        field: 'application_id',
        type: DataTypes.INTEGER,
        defaultValue: null,
      },
      assigneeId: {
        field: 'assignee_id',
        type: DataTypes.INTEGER,
        defaultValue: null,
      },
      clientId: {
        field: 'client_id',
        type: DataTypes.INTEGER,
        defaultValue: null,
      },
      reportId: {
        field: 'report_id',
        type: DataTypes.INTEGER,
        defaultValue: null,
      },
      funder: {
        type: DataTypes.STRING,
        defaultValue: null,
      },
      source: {
        type: DataTypes.SMALLINT,
        defaultValue: null,
      },
      category: {
        type: DataTypes.SMALLINT,
        defaultValue: null,
      },
      status: {
        type: DataTypes.ENUM,
        values: Object.keys(strings.awards.statuses),
        defaultValue: null,
      },
      startsOn: {
        field: 'starts_on',
        type: DataTypes.DATEONLY,
        defaultValue: null,
      },
      endsOn: {
        field: 'ends_on',
        type: DataTypes.DATE,
        defaultValue: null,
      },
      department: {
        type: DataTypes.STRING,
        defaultValue: null,
      },
      grantPurpose: {
        field: 'grant_purpose',
        type: DataTypes.STRING,
        defaultValue: null,
      },
      funderAwardId: {
        field: 'funder_award_id',
        type: DataTypes.STRING,
        defaultValue: null,
      },
      grantProgramName: {
        field: 'grant_program_name',
        type: DataTypes.STRING,
        defaultValue: null,
      },
      notes: {
        type: DataTypes.TEXT,
        defaultValue: null,
      },
      dateApproved: {
        field: 'date_approved',
        type: DataTypes.DATEONLY,
        defaultValue: null,
      },
      dateLastRejected: {
        field: 'date_last_rejected',
        type: DataTypes.DATEONLY,
        defaultValue: null,
      },
      isApproved: {
        field: 'is_approved',
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      isCompliance: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      complianceDate: {
        type: DataTypes.DATEONLY,
        defaultValue: null,
      },
      awardAmount: {
        field: 'award_amount',
        type: DataTypes.FLOAT,
        defaultValue: null,
      },
      awardExpended: {
        field: 'award_expended',
        type: DataTypes.FLOAT,
        defaultValue: null,
      },
      awardBalance: {
        field: 'award_balance',
        type: DataTypes.FLOAT,
        defaultValue: null,
      },
      matchAmount: {
        field: 'match_amount',
        type: DataTypes.FLOAT,
        defaultValue: null,
      },
      matchExpended: {
        field: 'match_expended',
        type: DataTypes.FLOAT,
        defaultValue: null,
      },
      matchBalance: {
        field: 'match_balance',
        type: DataTypes.FLOAT,
        defaultValue: null,
      },
      paymentsRequested: {
        field: 'payments_requested',
        type: DataTypes.FLOAT,
        defaultValue: 0,
      },
      paymentsReceived: {
        field: 'payments_received',
        type: DataTypes.FLOAT,
        defaultValue: 0,
      },
      resolutionRequired: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      applicationRequired: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      awardLetterRequired: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      contractMaterialRequired: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      initialAgreementRequired: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      reportRequired: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      awardPortalLink: {
        field: 'award_portal_link',
        type: DataTypes.STRING,
        defaultValue: null,
      },
      enabled: {
        field: 'enabled',
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
    },
    {
      defaultScope: {
        where: { enabled: true },
        // include: [
        //   {
        //     model: User,
        //     as: 'assignee',
        //     attributes: ['id', 'name'],
        //   },
        //   {
        //     model: Client,
        //     as: 'client',
        //     attributes: ['id', 'name'],
        //   },
        //   {
        //     model: Application,
        //     as: 'application',
        //     attributes: ['id', 'name'],
        //   },
        // ],
        attributes: {
          include: [
            [
              sequelize.literal(`(
                SELECT MIN("due_date")
                FROM "award_reports"
                WHERE award_id = "awards"."id"
                  AND "due_date" > NOW()
              )`),
              'nextReportDueDate',
            ],
          ],
        },
      },
      sequelize,
      modelName: 'awards',
    }
  );

  Awards.addHook('beforeCreate', (award) => {
    award.set('stringId', generateCustomId());
  });

  Awards.addHook('afterCreate', async (award) => {
    await Awards.sendStatusChangeEmail(award.dataValues, false);

    AwardBudgetEntry.bulkCreate(
      strings.awards.budgetEntryCategories.map((category: string) => ({
        awardId: award.getDataValue('id'),
        name: category,
        awardAmount: 0,
        awardExpended: 0,
        awardBalance: 0,
        matchAmount: 0,
        matchExpended: 0,
      }))
    );

    // Create user roles based on default users with default user roles assigned
    const defaultUserRoleUsers = await User.findAll({
      where: {
        clientCreatorId: award.getDataValue('clientId'),
        defaultRole: strings.awards.defaultAwardRoles,
        enabled: true,
      },
    });

    defaultUserRoleUsers.forEach((user: typeof User) => {
      const role = AwardUserRole.create({
        role: user.defaultRole,
        userId: user.id,
        awardId: award.getDataValue('id'),
      });

      if (user.userType.match(/admin/i)) return;

      AwardUserRole.sendRoleChangeEmail(award.getDataValue('id'), user.defaultRole, user.id, null);
    });
  });

  Awards.addHook('beforeUpdate', (award) => {
    const changes = (award.changed() as string[]) || [];

    if (changes.includes('assigneeId')) {
      Awards.updateAssigneeRole(award.dataValues);
    }
  });

  Awards.addHook('afterUpdate', (award) => {
    const changes = (award.changed() as string[]) || [];

    if (changes.includes('status')) {
      Awards.sendStatusChangeEmail(award.dataValues, true);
      Awards.createStatusChangeNotifications(award.dataValues, award.previous());
    }
  });

  return Awards;
}
