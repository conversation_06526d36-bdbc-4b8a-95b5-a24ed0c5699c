-- Script to select programs with human-readable values for sources, categories, and org types
-- Converts numeric codes (0,1,2,3...) to descriptive text
--
-- USAGE:
-- This script provides multiple queries to view programs with decoded values:
-- 1. Basic program listing with human-readable fields
-- 2. Programs grouped by source type
-- 3. Programs grouped by category
-- 4. Programs with multiple sources/categories expanded
-- 5. Summary statistics by source and category
--
-- FIELD MAPPINGS:
-- Sources: 0=Federal, 1=State, 2=Regional, 3=County, 4=Local, 5=Philanthropic, 6=Other
-- Categories: 0=Environmental, 1=Transportation, 2=Health, 3=Public Safety, 4=Parks & Open Space, 
--            5=Library, 6=Recreation, 7=Historic Preservation, 8=Community Development, 
--            9=Economic Development, 10=Education, 11=Justice, 12=Housing, 13=Arts & Humanities, 
--            14=Youth, 15=Disaster Recovery, 16=Workforce Development, 17=General
-- OrgTypes: Already stored as text (Municipality, County, Nonprofit, School, Other)

-- Helper function to decode source arrays
CREATE OR REPLACE FUNCTION decode_sources(source_array INTEGER[])
RETURNS TEXT AS $$
DECLARE
    source_names TEXT[] := ARRAY['Federal', 'State', 'Regional', 'County', 'Local', 'Philanthropic', 'Other'];
    result TEXT := '';
    source_val INTEGER;
BEGIN
    IF source_array IS NULL OR array_length(source_array, 1) IS NULL THEN
        RETURN 'Not Specified';
    END IF;

    FOREACH source_val IN ARRAY source_array
    LOOP
        IF source_val >= 0 AND source_val < array_length(source_names, 1) THEN
            IF result != '' THEN
                result := result || ', ';
            END IF;
            result := result || source_names[source_val + 1];
        END IF;
    END LOOP;

    RETURN CASE WHEN result = '' THEN 'Unknown' ELSE result END;
END;
$$ LANGUAGE plpgsql;

-- Helper function to decode category arrays
CREATE OR REPLACE FUNCTION decode_categories(category_array INTEGER[])
RETURNS TEXT AS $$
DECLARE
    category_names TEXT[] := ARRAY[
        'Environmental', 'Transportation', 'Health', 'Public Safety', 'Parks & Open Space',
        'Library', 'Recreation', 'Historic Preservation', 'Community Development',
        'Economic Development', 'Education', 'Justice', 'Housing', 'Arts & Humanities',
        'Youth', 'Disaster Recovery', 'Workforce Development', 'General'
    ];
    result TEXT := '';
    category_val INTEGER;
BEGIN
    IF category_array IS NULL OR array_length(category_array, 1) IS NULL THEN
        RETURN 'Not Specified';
    END IF;
    
    FOREACH category_val IN ARRAY category_array
    LOOP
        IF category_val >= 0 AND category_val < array_length(category_names, 1) THEN
            IF result != '' THEN
                result := result || ', ';
            END IF;
            result := result || category_names[category_val + 1];
        END IF;
    END LOOP;
    
    RETURN CASE WHEN result = '' THEN 'Unknown' ELSE result END;
END;
$$ LANGUAGE plpgsql;

-- Query 1: Basic program listing with human-readable values
SELECT 
    id,
    name,
    funder,
    funding_amount,
    CASE 
        WHEN amount_varies THEN 'Yes (' || COALESCE(varying_funding_amount, 'Amount varies') || ')'
        ELSE 'No'
    END as amount_varies_display,
    decode_sources(source) as sources,
    decode_categories(category) as categories,
    CASE 
        WHEN org_types IS NULL OR array_length(org_types, 1) IS NULL THEN 'All Types'
        ELSE array_to_string(org_types, ', ')
    END as organization_types,
    CASE 
        WHEN states IS NULL OR array_length(states, 1) IS NULL THEN 'All States'
        ELSE array_to_string(states, ', ')
    END as eligible_states,
    CASE 
        WHEN counties IS NULL OR array_length(counties, 1) IS NULL THEN 'All Counties'
        ELSE array_to_string(counties, ', ')
    END as eligible_counties,
    starts_at,
    ends_at,
    match_requirements,
    performance_period,
    estimated_response,
    CASE 
        WHEN show_for_flex_client THEN 'Yes'
        ELSE 'No'
    END as show_for_flex_client
FROM programs
WHERE id IS NOT NULL
ORDER BY name;

-- Query 2: Programs grouped by source type
SELECT 
    'PROGRAMS BY SOURCE TYPE' as section;

SELECT 
    decode_sources(ARRAY[source_val]) as source_type,
    COUNT(*) as program_count,
    ROUND(AVG(funding_amount), 2) as avg_funding_amount,
    SUM(funding_amount) as total_funding_amount
FROM programs p,
     unnest(COALESCE(p.source, ARRAY[NULL::INTEGER])) as source_val
WHERE source_val IS NOT NULL
GROUP BY source_val
ORDER BY source_val;

-- Query 3: Programs grouped by category
SELECT 
    'PROGRAMS BY CATEGORY' as section;

SELECT 
    decode_categories(ARRAY[category_val]) as category_type,
    COUNT(*) as program_count,
    ROUND(AVG(funding_amount), 2) as avg_funding_amount,
    SUM(funding_amount) as total_funding_amount
FROM programs p,
     unnest(COALESCE(p.category, ARRAY[NULL::INTEGER])) as category_val
WHERE category_val IS NOT NULL
GROUP BY category_val
ORDER BY category_val;

-- Query 4: Programs with organization type breakdown
SELECT 
    'PROGRAMS BY ORGANIZATION TYPE' as section;

SELECT 
    org_type,
    COUNT(*) as program_count,
    ROUND(AVG(funding_amount), 2) as avg_funding_amount
FROM programs p,
     unnest(COALESCE(p.org_types, ARRAY['All Types'])) as org_type
GROUP BY org_type
ORDER BY org_type;

-- Query 5: Detailed program view with expanded arrays
SELECT 
    'DETAILED PROGRAM VIEW' as section;

SELECT 
    p.id,
    p.name,
    p.funder,
    p.funding_amount,
    decode_sources(ARRAY[source_val]) as individual_source,
    decode_categories(ARRAY[category_val]) as individual_category,
    org_type as organization_type,
    p.starts_at,
    p.ends_at
FROM programs p
LEFT JOIN unnest(COALESCE(p.source, ARRAY[NULL::INTEGER])) as source_val ON true
LEFT JOIN unnest(COALESCE(p.category, ARRAY[NULL::INTEGER])) as category_val ON true  
LEFT JOIN unnest(COALESCE(p.org_types, ARRAY['All Types'])) as org_type ON true
WHERE p.id IS NOT NULL
ORDER BY p.name, source_val, category_val, org_type;

-- Query 6: Summary statistics
SELECT 
    'SUMMARY STATISTICS' as section;

SELECT 
    COUNT(*) as total_programs,
    COUNT(CASE WHEN source IS NOT NULL AND array_length(source, 1) > 0 THEN 1 END) as programs_with_source,
    COUNT(CASE WHEN category IS NOT NULL AND array_length(category, 1) > 0 THEN 1 END) as programs_with_category,
    COUNT(CASE WHEN org_types IS NOT NULL AND array_length(org_types, 1) > 0 THEN 1 END) as programs_with_org_types,
    COUNT(CASE WHEN funding_amount IS NOT NULL THEN 1 END) as programs_with_funding_amount,
    ROUND(AVG(funding_amount), 2) as avg_funding_amount,
    MIN(funding_amount) as min_funding_amount,
    MAX(funding_amount) as max_funding_amount,
    SUM(funding_amount) as total_funding_amount
FROM programs;

-- Query 7: Programs missing key information
SELECT 
    'PROGRAMS MISSING KEY INFORMATION' as section;

SELECT 
    id,
    name,
    funder,
    CASE WHEN source IS NULL OR array_length(source, 1) IS NULL THEN 'Missing Source' ELSE 'Has Source' END as source_status,
    CASE WHEN category IS NULL OR array_length(category, 1) IS NULL THEN 'Missing Category' ELSE 'Has Category' END as category_status,
    CASE WHEN org_types IS NULL OR array_length(org_types, 1) IS NULL THEN 'Missing Org Types' ELSE 'Has Org Types' END as org_types_status,
    CASE WHEN funding_amount IS NULL THEN 'Missing Funding Amount' ELSE 'Has Funding Amount' END as funding_status
FROM programs
WHERE (source IS NULL OR array_length(source, 1) IS NULL)
   OR (category IS NULL OR array_length(category, 1) IS NULL)
   OR (org_types IS NULL OR array_length(org_types, 1) IS NULL)
   OR funding_amount IS NULL
ORDER BY name;

-- Clean up helper functions (optional - comment out if you want to keep them)
-- DROP FUNCTION IF EXISTS decode_sources(INTEGER[]);
-- DROP FUNCTION IF EXISTS decode_categories(INTEGER[]);
