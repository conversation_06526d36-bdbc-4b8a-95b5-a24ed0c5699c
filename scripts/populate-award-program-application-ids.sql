-- Script to populate program_id and application_id for awards
-- Based on matching funder, client_id, and grant_program_name
--
-- USAGE:
-- 1. Run this script after importing awards (e.g., after linden-awards-import.sql)
-- 2. The script will attempt multiple matching strategies in order of specificity
-- 3. Review the reports at the end to see matching success rates
-- 4. Optionally uncomment the program creation section to auto-create missing programs
--
-- MATCHING STRATEGY:
-- 1. Exact match: funder + grant_program_name = programs.funder + programs.name
-- 2. Abbreviation match: Handle common abbreviations (NPP, LFIF, CDBG, etc.)
-- 3. Fiscal year normalization: Remove FY## and year suffixes for broader matching
-- 4. Application matching: Match awards to applications via program_id + client_id
--
-- Run this after importing awards to establish proper relationships

-- Step 1: Update program_id for awards based on exact funder and grant_program_name matching
UPDATE awards
SET program_id = programs.id
FROM programs
WHERE awards.program_id IS NULL
  AND awards.funder = programs.funder
  AND awards.grant_program_name = programs.name
  AND awards.enabled = true;

-- Step 2: Update application_id for awards based on program_id and client_id matching
UPDATE awards
SET application_id = applications.id
FROM applications
WHERE awards.application_id IS NULL
  AND awards.program_id = applications.program_id
  AND awards.client_id = applications.client_id
  AND awards.enabled = true;

-- Step 3: Alternative matching for programs using fuzzy/partial matching
-- This handles cases where program names might not match exactly
-- Uncomment and adjust the LIKE patterns as needed for your data

/*
UPDATE awards 
SET program_id = programs.id
FROM programs 
WHERE awards.program_id IS NULL
  AND awards.funder = programs.funder
  AND (
    -- Add fuzzy matching patterns here
    programs.name ILIKE '%' || awards.grant_program_name || '%'
    OR awards.grant_program_name ILIKE '%' || programs.name || '%'
    -- Example specific patterns:
    -- OR (awards.grant_program_name ILIKE '%NPP%' AND programs.name ILIKE '%Neighborhood Preservation%')
    -- OR (awards.grant_program_name ILIKE '%LFIF%' AND programs.name ILIKE '%Local Freight Impact%')
  )
  AND awards.enabled = true
  AND programs.enabled = true;
*/

-- Step 4: Report on matching results
-- Show awards that still don't have program_id assigned
SELECT
  'Awards without program_id' as report_type,
  COUNT(*) as count
FROM awards
WHERE program_id IS NULL
  AND enabled = true;

-- Show unique funders for unmatched awards
SELECT DISTINCT funder as unmatched_funders
FROM awards
WHERE program_id IS NULL
  AND enabled = true
ORDER BY funder;

-- Show awards that still don't have application_id assigned
SELECT
  'Awards without application_id' as report_type,
  COUNT(*) as count
FROM awards
WHERE application_id IS NULL
  AND program_id IS NOT NULL
  AND enabled = true;

-- Step 5: Detailed report of unmatched awards for manual review
SELECT 
  string_id,
  funder,
  grant_program_name,
  client_id,
  program_id,
  application_id,
  'Missing program match' as issue
FROM awards 
WHERE program_id IS NULL 
  AND enabled = true
ORDER BY funder, grant_program_name;

SELECT 
  string_id,
  funder,
  grant_program_name,
  client_id,
  program_id,
  application_id,
  'Missing application match' as issue
FROM awards 
WHERE application_id IS NULL 
  AND program_id IS NOT NULL
  AND enabled = true
ORDER BY funder, grant_program_name;

-- Step 6: Summary report of successful matches
SELECT
  'Successfully matched awards' as report_type,
  COUNT(*) as total_awards,
  COUNT(program_id) as awards_with_program,
  COUNT(application_id) as awards_with_application,
  ROUND(COUNT(program_id) * 100.0 / COUNT(*), 2) as program_match_percentage,
  ROUND(COUNT(application_id) * 100.0 / COUNT(*), 2) as application_match_percentage
FROM awards
WHERE enabled = true;

-- Step 7: Advanced matching strategies for specific cases
-- Handle common program name variations and abbreviations

-- Match Neighborhood Preservation Program variations
UPDATE awards
SET program_id = programs.id
FROM programs
WHERE awards.program_id IS NULL
  AND awards.funder = programs.funder
  AND awards.enabled = true
  AND (
    (UPPER(awards.grant_program_name) LIKE '%NPP%' AND UPPER(programs.name) LIKE '%NEIGHBORHOOD PRESERVATION%')
    OR (UPPER(awards.grant_program_name) LIKE '%NEIGHBORHOOD PRESERVATION%' AND UPPER(programs.name) LIKE '%NPP%')
  );

-- Match Local Freight Impact Fund variations
UPDATE awards
SET program_id = programs.id
FROM programs
WHERE awards.program_id IS NULL
  AND awards.funder = programs.funder
  AND awards.enabled = true
  AND (
    (UPPER(awards.grant_program_name) LIKE '%LFIF%' AND UPPER(programs.name) LIKE '%LOCAL FREIGHT IMPACT%')
    OR (UPPER(awards.grant_program_name) LIKE '%LOCAL FREIGHT IMPACT%' AND UPPER(programs.name) LIKE '%LFIF%')
  );

-- Match Community Development Block Grant variations
UPDATE awards
SET program_id = programs.id
FROM programs
WHERE awards.program_id IS NULL
  AND awards.funder = programs.funder
  AND awards.enabled = true
  AND (
    (UPPER(awards.grant_program_name) LIKE '%CDBG%' AND UPPER(programs.name) LIKE '%COMMUNITY DEVELOPMENT BLOCK%')
    OR (UPPER(awards.grant_program_name) LIKE '%COMMUNITY DEVELOPMENT BLOCK%' AND UPPER(programs.name) LIKE '%CDBG%')
  );

-- Step 8: Match by removing fiscal year suffixes for broader matching
-- This handles cases where program names are the same but have different FY suffixes
-- Note: Using simpler pattern matching for better compatibility
UPDATE awards
SET program_id = programs.id
FROM programs
WHERE awards.program_id IS NULL
  AND awards.funder = programs.funder
  AND awards.enabled = true
  AND (
    -- Try matching after removing common FY patterns
    REPLACE(REPLACE(UPPER(awards.grant_program_name), ' FY24', ''), ' FY23', '') =
    REPLACE(REPLACE(UPPER(programs.name), ' FY24', ''), ' FY23', '')

    -- Also try removing 2023, 2024 year patterns
    OR REPLACE(REPLACE(UPPER(awards.grant_program_name), ' 2024', ''), ' 2023', '') =
       REPLACE(REPLACE(UPPER(programs.name), ' 2024', ''), ' 2023', '')
  );

-- Step 9: Final comprehensive report
SELECT
  'FINAL MATCHING REPORT' as section;

SELECT
  funder,
  COUNT(*) as total_awards,
  COUNT(program_id) as matched_programs,
  COUNT(application_id) as matched_applications,
  CASE
    WHEN COUNT(*) > 0 THEN CAST(COUNT(program_id) * 100 / COUNT(*) AS INTEGER)
    ELSE 0
  END as program_match_pct,
  CASE
    WHEN COUNT(*) > 0 THEN CAST(COUNT(application_id) * 100 / COUNT(*) AS INTEGER)
    ELSE 0
  END as app_match_pct
FROM awards
WHERE enabled = true
GROUP BY funder
ORDER BY funder;

-- Optional: Create missing programs for unmatched awards
-- Uncomment the section below if you want to automatically create programs

/*
INSERT INTO programs (
  name,
  funder,
  "createdAt",
  "updatedAt"
)
SELECT DISTINCT
  awards.grant_program_name,
  awards.funder,
  NOW(),
  NOW()
FROM awards
LEFT JOIN programs ON (
  awards.funder = programs.funder
  AND awards.grant_program_name = programs.name
)
WHERE awards.program_id IS NULL
  AND awards.enabled = true
  AND programs.id IS NULL;

-- After creating programs, run the basic matching again
UPDATE awards
SET program_id = programs.id
FROM programs
WHERE awards.program_id IS NULL
  AND awards.funder = programs.funder
  AND awards.grant_program_name = programs.name
  AND awards.enabled = true;
*/
